using HutongGames.PlayMaker;
using Fish;
using UnityEngine;

namespace Fish.PlayMaker
{
    [ActionCategory("Fish/GameObject")]
    [HutongGames.PlayMaker.Tooltip("加载预制体并存储到变量")]
    public class LoadPrefabAction : FsmStateAction
    {
        [HutongGames.PlayMaker.Tooltip("资源包名")]
        public FsmString bundleName;

        [HutongGames.PlayMaker.Tooltip("是否使用Store的预制体名")]
        public bool UseStorePrefabName;

        [HutongGames.PlayMaker.Tooltip("预制体名")]
        public FsmString prefabName;

        [HutongGames.PlayMaker.Tooltip("Store的预制体名")]
        public string StorePrefabNameKey;

        [HutongGames.PlayMaker.Tooltip("是否使用Store的父节点")]
        public bool UseStoreParent;
        [HutongGames.PlayMaker.Tooltip("父节点")]
        public FsmOwnerDefault Parent;
        public string StoreParentKey;

        [HutongGames.PlayMaker.Tooltip("是否存储到变量")]
        public bool SaveToVariable;

        [HutongGames.PlayMaker.Tooltip("存储到哪个变量")]
        public FsmGameObject StoreVariable;

        [HutongGames.PlayMaker.Tooltip("Store的key")]
        public string StoreKey;

        public override void OnEnter()
        {
            GameObject parentObj = null;

            if (UseStoreParent)
            {
                parentObj = Owner.gameObject.GetDataObject(StoreParentKey) as GameObject;
            }
            else
            {
                parentObj = Fsm.GetOwnerDefaultTarget(Parent);
            }

            var prefabNameStr = string.Empty;
            
            if(UseStorePrefabName)
            {
                prefabNameStr = Owner.gameObject.GetDataObject(StorePrefabNameKey) as string;
            }
            else
            {
                prefabNameStr = prefabName.Value;
            }

            Transform parentTransform = parentObj ? parentObj.transform : null;
            GameObject obj = Asset.LoadPrefab(parentTransform, bundleName.Value, prefabNameStr);

            if (obj != null)
            {
                if (SaveToVariable)
                {
                    StoreVariable.Value = obj;
                }
                else
                {
                    Owner.gameObject.SetDataObject(StoreKey, obj);
                }
            }

            Finish();
        }
    }
}