using System;
using UnityEngine;
using UnityEngine.UI;

namespace Fish.PlayMaker
{
    public class PlayMakerUnitTest_TextMesh : PlayMakerUnitTestPanelBase
    {
        public Button Btn_Alignment;
        public Button Btn_Anchor;
        public Button Btn_CharacterSize;
        public Button Btn_Color;
        public Button Btn_Font;
        public Button Btn_FontSize;
        public Button Btn_FontStyle;
        public Button Btn_LineSpacing;
        public Button Btn_OffsetZ;
        public Button Btn_RichText;
        public Button Btn_TabSize;
        public Button Btn_Text;
        
        private void Awake()
        {
            // 绑定所有按钮的点击事件
            Btn_Alignment?.onClick.AddListener(OnClickAlignment);
            Btn_Anchor?.onClick.AddListener(OnClickAnchor);
            Btn_CharacterSize?.onClick.AddListener(OnClickCharacterSize);
            Btn_Color?.onClick.AddListener(OnClickColor);
            Btn_Font?.onClick.AddListener(OnClickFont);
            Btn_FontSize?.onClick.AddListener(OnClickFontSize);
            Btn_FontStyle?.onClick.AddListener(OnClickFontStyle);
            Btn_LineSpacing?.onClick.AddListener(OnClickLineSpacing);
            Btn_OffsetZ?.onClick.AddListener(OnClickOffsetZ);
            Btn_RichText?.onClick.AddListener(OnClickRichText);
            Btn_TabSize?.onClick.AddListener(OnClickTabSize);
            Btn_Text?.onClick.AddListener(OnClickText);
        }

        private void OnClickAlignment()
        {
            Call("ClickAlignment");
        }

        private void OnClickAnchor()
        {
            Call("ClickAnchor");
        }

        private void OnClickCharacterSize()
        {
            Call("ClickCharacterSize");
        }

        private void OnClickColor()
        {
            Call("ClickColor");
        }

        private void OnClickFont()
        {
            Call("ClickFont");
        }

        private void OnClickFontSize()
        {
            Call("ClickFontSize");
        }

        private void OnClickFontStyle()
        {
            Call("ClickFontStyle");
        }

        private void OnClickLineSpacing()
        {
            Call("ClickLineSpacing");
        }

        private void OnClickOffsetZ()
        {
            Call("ClickOffsetZ");
        }

        private void OnClickRichText()
        {
            Call("ClickRichText");
        }

        private void OnClickTabSize()
        {
            Call("ClickTabSize");
        }

        private void OnClickText()
        {
            Call("ClickText");
        }

    }
}