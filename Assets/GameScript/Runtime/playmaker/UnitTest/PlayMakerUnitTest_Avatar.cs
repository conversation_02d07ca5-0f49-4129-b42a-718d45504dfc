using System;
using UnityEngine.UI;

namespace Fish.PlayMaker
{
    public class PlayMakerUnitTest_Avatar : PlayMakerUnitTestPanelBase
    {
        public Button BtnSetTargetMarkPosition;
        public Button BtnSyncAnimatorByServerTime;

        private void Awake()
        {
            BtnSetTargetMarkPosition.onClick.AddListener(OnClickSetTargetMarkPosition);
            BtnSyncAnimatorByServerTime.onClick.AddListener(OnClickSyncAnimatorByServerTime);
        }

        private void OnClickSyncAnimatorByServerTime()
        {
            Call("ClickSyncAnimatorByServerTime");
        }

        private void OnClickSetTargetMarkPosition()
        {
            Call("ClickSetTargetMarkPosition");
        }
    }
}