using System;
using UnityEngine;
using UnityEngine.UI;

namespace Fish.PlayMaker
{
    public class PlayMakerUnitTest : MonoBehaviour
    {
        #region 按钮

        public Button btnAudio;
        public Button btnAvatar;
        public Button btnCamera;
        public Button btnCommon;
        public Button btnDataStore;
        public Button btnEvent;
        public Button btnGameEffect;
        public Button btnGameObject;
        public Button btnJson;
        public Button btnNetwork;
        public Button btnReflect;
        public Button btnTextMesh;
        public Button btnUI;

        #endregion

        public GameObject goEntrance;
        public GameObject goPanel;
        public Image ImgStatus;

        #region 各功能面板
        public GameObject goPanelAudio;
        public GameObject goPanelAvatar;
        public GameObject goPanelCamera;
        public GameObject goPanelCommon;
        public GameObject goPanelDataStore;
        public GameObject goPanelEvent;
        public GameObject goPanelGameEffect;
        public GameObject goPanelGameObject;
        public GameObject goPanelJson;
        public GameObject goPanelNetwork;
        public GameObject goPanelReflect;
        public GameObject goPanelTextMesh;
        public GameObject goPanelUI;
        #endregion

        private void Awake()
        {
            BindUIEvent();
            StateFail();
            HideAllPanel();
        }

        private void BindUIEvent()
        {
            // 绑定所有按钮的点击事件
            btnAudio?.onClick.AddListener(OnBtnAudio);
            btnAvatar?.onClick.AddListener(OnBtnAvatar);
            btnCamera?.onClick.AddListener(OnBtnCamera);
            btnCommon?.onClick.AddListener(OnBtnCommon);
            btnDataStore?.onClick.AddListener(OnBtnDataStore);
            btnEvent?.onClick.AddListener(OnBtnEvent);
            btnGameEffect?.onClick.AddListener(OnBtnGameEffect);
            btnGameObject?.onClick.AddListener(OnBtnGameObject);
            btnJson?.onClick.AddListener(OnBtnJson);
            btnNetwork?.onClick.AddListener(OnBtnNetwork);
            btnReflect?.onClick.AddListener(OnBtnReflect);
            btnTextMesh?.onClick.AddListener(OnBtnTextMesh);
            btnUI?.onClick.AddListener(OnBtnUI);
        }

        public void ClickClose()
        {
            HideAllPanel();
            goEntrance.SetActive(true);
            StateFail();
        }

        public void StateFail()
        {
            ImgStatus.color = Color.red;
        }

        private void HideAllPanel()
        {
            foreach (Transform child in goPanel.transform)
            {
                child.gameObject.SetActive(false);
            }
        }

        public void OnBtnAudio()
        {
            goEntrance.SetActive(false);
            HideAllPanel();
            goPanelAudio.SetActive(true);
        }

        public void OnBtnAvatar()
        {
            goEntrance.SetActive(false);
            HideAllPanel();
            goPanelAvatar.SetActive(true);
        }

        public void OnBtnCamera()
        {
            goEntrance.SetActive(false);
            HideAllPanel();
            goPanelCamera.SetActive(true);
        }

        public void OnBtnCommon()
        {
            goEntrance.SetActive(false);
            HideAllPanel();
            goPanelCommon.SetActive(true);
        }

        public void OnBtnDataStore()
        {
            goEntrance.SetActive(false);
            HideAllPanel();
            goPanelDataStore.SetActive(true);
        }

        public void OnBtnEvent()
        {
            goEntrance.SetActive(false);
            HideAllPanel();
            goPanelEvent.SetActive(true);
        }

        public void OnBtnGameEffect()
        {
            goEntrance.SetActive(false);
            HideAllPanel();
            goPanelGameEffect.SetActive(true);
        }

        public void OnBtnGameObject()
        {
            goEntrance.SetActive(false);
            HideAllPanel();
            goPanelGameObject.SetActive(true);
        }

        public void OnBtnJson()
        {
            goEntrance.SetActive(false);
            HideAllPanel();
            goPanelJson.SetActive(true);
        }

        public void OnBtnNetwork()
        {
            goEntrance.SetActive(false);
            HideAllPanel();
            goPanelNetwork.SetActive(true);
        }

        public void OnBtnReflect()
        {
            goEntrance.SetActive(false);
            HideAllPanel();
            goPanelReflect.SetActive(true);
        }

        public void OnBtnTextMesh()
        {
            goEntrance.SetActive(false);
            HideAllPanel();
            goPanelTextMesh.SetActive(true);
        }

        public void OnBtnUI()
        {
            goEntrance.SetActive(false);
            HideAllPanel();
            goPanelUI.SetActive(true);
        }
    }
}