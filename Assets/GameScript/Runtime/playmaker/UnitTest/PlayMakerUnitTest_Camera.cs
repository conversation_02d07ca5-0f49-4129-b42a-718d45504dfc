using System;
using UnityEngine.UI;

namespace Fish.PlayMaker
{
    public class PlayMakerUnitTest_Camera : PlayMakerUnitTestPanelBase
    {
        public Button BtnCustomShake;
        public Button BtnShake1;
        public Button BtnShake2;

        private void Awake()
        {
            BtnCustomShake.onClick.AddListener(OnClickCustomShake);
            BtnShake1.onClick.AddListener(OnClickShake1);
            BtnShake2.onClick.AddListener(OnClickShake2);
        }

        private void OnClickShake2()
        {
            Call("ClickShake2");
        }

        private void OnClickShake1()
        {
            Call("ClickShake1");
        }

        private void OnClickCustomShake()
        {
            Call("ClickCustomShake");
        }
    }
}