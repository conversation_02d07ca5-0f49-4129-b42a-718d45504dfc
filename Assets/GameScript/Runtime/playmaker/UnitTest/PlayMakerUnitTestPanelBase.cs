using System;
using UnityEngine;
using UnityEngine.UI;

namespace Fish.PlayMaker
{
    public abstract class PlayMakerUnitTestPanelBase : MonoBehaviour
    {
        private void OnEnable()
        {
            StateFail();
        }
        
        private void StateFail()
        {
            transform.parent.parent.Find("Status").GetComponent<Image>().color = Color.red;
        }

        protected void Call(string eventName)
        {
            var fsm = FindFSMByName("FSM");
            if (fsm != null)
            {
                fsm.Fsm.Event(eventName);
            }
            else
            {
                Debug.LogError($"未找到名为 FSM 的 FSM 组件");
            }
        }

        private PlayMakerFSM FindFSMByName(string fsmName)
        {
            PlayMakerFSM[] fsmComponents = GetComponents<PlayMakerFSM>();
            foreach (var fsm in fsmComponents)
            {
                if (fsm.FsmName == fsmName)
                {
                    return fsm;
                }
            }

            return null;
        }
    }
}