using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEditor.Build.Player;
using System.IO;

public class BuildTools : MonoBehaviour
{
    private static string PRODUCT_NAME = "tyby";
    private static string PACKAGE_NAME = "ios.tuyoo.fish.tyby";
    private static string VERSION_NAME = "4.03";
    private static string COMPANY_NAME = "tuyoo";
    public static string DEFINE_SYMBOLS = "";
    private static int ANTI_ALIASING = 0;

    // Android
    private static int VERSION_CODE = 1234;
    private static string KEYSTORE_NAME = "/Users/<USER>/workspace/tuyoo/newsvn/fish/FishU2D/frameworks/FishAndroidFramework/key/tuyoo.keystore";
    private static string KEYSTORE_PASS = "tuyougame";
    private static string KEYALIAS_NAME = "dizhu";
    private static string KEYALIAS_PASS = "tuyougame";

    // iOS
    private static int BUILD_NUMBER = 1;
    private static string APPLE_DEVELOPER_TEAM_ID = "R6D8ASM82Z";
    private static string PROVISIONING_PROFILE_ID = "250b5f00-72ec-4186-9abb-e1af144719f4";


    [MenuItem("BuildTools/BuildIOS")]
    static void BuildIOS()
    {
#if FISH_DEBUG
        string[] levels = new string[] {
            "Assets/GameRes/Scenes/boot.unity",
            "Assets/GameRes/Scenes/launcher.unity",
            "Assets/GameRes/Scenes/reload.unity"
        };
#else
		string[] levels = new string[] {
			"Assets/GameRes/Scenes/launcher.unity",
			"Assets/GameRes/Scenes/reload.unity"
		};
#endif

#if !UWA_TEST
        AssetDatabase.DeleteAsset("Assets/UWA");
        AssetDatabase.DeleteAsset("Assets/Plugins/iOS/UwaTools.mm");
        AssetDatabase.DeleteAsset("Assets/Plugins/iOS/UwaTools.h");
#endif

        UpdateAssets();
        ChangeiOSSettings();
        AppBuildPrepare();

        // 正式版
        BuildPipeline.BuildPlayer(levels, "./Output/iOS", BuildTarget.iOS, BuildOptions.None);

        // 开发者版本
        // BuildOptions buildOptions = BuildOptions.None;
        // // buildOptions |= BuildOptions.AcceptExternalModificationsToPlayer;
        // buildOptions |= BuildOptions.Development;
        // buildOptions |= BuildOptions.ConnectWithProfiler;
        // buildOptions |= BuildOptions.AllowDebugging;
        // BuildPipeline.BuildPlayer(levels, "./Output/iOS", BuildTarget.iOS, buildOptions);
    }

    [MenuItem("BuildTools/BuildMacOS")]
    static void BuildMacOS()
    {
        UpdateAssets();
        CreateAssetBundles.BuildMacNew();
        ChangeiOSSettings();
        BuildPipeline.BuildPlayer(EditorBuildSettings.scenes, "./Output/MacOS", EditorUserBuildSettings.activeBuildTarget, BuildOptions.None);
    }

    [MenuItem("BuildTools/BuildAndroid")]
    static void BuildAndroid()
    {
        UpdateAssets();
        // CreateAssetBundles.BuildAndroid();
        ChangeAndroidSetting();
        AppBuildPrepare();

#if UNITY_HW
        BuildPipeline.BuildPlayer(EditorBuildSettings.scenes, "./Output/Android", BuildTarget.Android, BuildOptions.AcceptExternalModificationsToPlayer);
#else
        BuildPipeline.BuildPlayer(EditorBuildSettings.scenes, "./Output/Android.apk", BuildTarget.Android, BuildOptions.None);
#endif
    }

    [MenuItem("BuildTools/ExportAndroid")]
    static void ExportAndroidNew()
    {
        UpdateAssets();
        ChangeAndroidSetting();
        AppBuildPrepare();

        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);

#if FISH_EMULATOR
        PlayerSettings.Android.targetArchitectures = AndroidArchitecture.All;
        PlayerSettings.Android.buildApkPerCpuArchitecture = false;
#else
        PlayerSettings.Android.targetArchitectures = AndroidArchitecture.ARMv7 | AndroidArchitecture.ARM64;
        PlayerSettings.Android.buildApkPerCpuArchitecture = false;
#endif

#if FISH_DEV
		// 注意：开发者版本
		BuildOptions buildOptions = BuildOptions.None;
		buildOptions |= BuildOptions.Development;
		buildOptions |= BuildOptions.ConnectWithProfiler;
		//buildOptions |= BuildOptions.AllowDebugging;
        buildOptions |= BuildOptions.AcceptExternalModificationsToPlayer;
		EditorUserBuildSettings.exportAsGoogleAndroidProject = true;
#else
        // 注意：正式版本
        BuildOptions buildOptions = BuildOptions.None;
        buildOptions |= BuildOptions.AcceptExternalModificationsToPlayer;
        EditorUserBuildSettings.exportAsGoogleAndroidProject = true;
#endif

#if FISH_SYMBOLS
        EditorUserBuildSettings.androidCreateSymbolsZip = true;
#endif

#if FISH_INTERNATIONAL
		EditorUserBuildSettings.buildAppBundle = true;
#endif

#if FISH_DEBUG || UWA_TEST
        string[] levels = new string[] { "Assets/GameRes/Scenes/boot.unity", "Assets/GameRes/Scenes/launcher.unity", "Assets/GameRes/Scenes/reload.unity" };
#else
        string[] levels = new string[] { "Assets/GameRes/Scenes/launcher.unity","Assets/GameRes/Scenes/reload.unity"};
#endif

        // 开始打包
        Debug.Log("开始打包！");
        var buildReport = BuildPipeline.BuildPlayer(levels, "./Output/Android/", BuildTarget.Android, buildOptions);
        if (buildReport.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
        {
            Debug.Log("打包成功！");
        }
        else
        {
            Debug.LogError($"打包发生错误数量：{buildReport.summary.totalErrors}");
            throw new System.Exception("打包失败！");
        }
    }

    [MenuItem("BuildTools/ExportPlugin")]
    static void ExportPlugin()
    {
#if UNITY_ANDROID
        UpdateAssets();
        // CreateAssetBundles.BuildAndroid();
        ChangeAndroidSetting();
        AppBuildPrepare();

        // EditorBuildSettings.exportAsGoogleAndroidProject = true;
        string[] levels = new string[] {
            "Assets/GameRes/Scenes/launcher.unity",
            "Assets/GameRes/Scenes/boot.unity",
            "Assets/GameRes/Scenes/reload.unity",
            "Assets/GameRes/Scenes/loading.unity",
            "Assets/GameRes/Scenes/login.unity",
            "Assets/GameRes/Scenes/lobby.unity",
            "Assets/GameRes/Scenes/game.unity"
        };

#if UNITY_2020_3_OR_NEWER
        //if (BuildPipeline.BuildCanBeAppended(BuildTarget.iOS, "iOSBuild") == CanAppendBuild.Yes) 
        //buildPlayerOptions.options |= BuildOptions.AcceptExternalModificationsToPlayer;

        var options = BuildOptions.AllowDebugging;
        EditorUserBuildSettings.exportAsGoogleAndroidProject = true;
#else
		var options = BuildOptions.AcceptExternalModificationsToPlayer;
#endif

        BuildPipeline.BuildPlayer(levels, "./Output/Android", BuildTarget.Android, options);
#endif
    }

    static void ExportTrialGame()
    {
#if UNITY_ANDROID
        UpdateAssets();
        // CreateAssetBundles.BuildAndroid();
        ChangeAndroidSetting();
        AppBuildPrepare();

        // EditorBuildSettings.exportAsGoogleAndroidProject = true;
        string[] levels = new string[] {
            "Assets/GameRes/Scenes/launcher.unity",
            "Assets/GameRes/Scenes/trial.unity",
            "Assets/GameRes/Scenes/loading.unity",
            "Assets/GameRes/Scenes/game.unity"
        };
        BuildPipeline.BuildPlayer(levels, "./Output/Android", BuildTarget.Android, BuildOptions.AcceptExternalModificationsToPlayer);
#endif
    }

    static void BuildAndroidApk()
    {
        BuildPipeline.BuildPlayer(EditorBuildSettings.scenes, "./Output/Android.apk", BuildTarget.Android, BuildOptions.None);
    }

    public static void ChangePlayerSettings()
    {
        // PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);
        PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.Standalone, DEFINE_SYMBOLS);
        PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android, DEFINE_SYMBOLS);
        PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.iOS, DEFINE_SYMBOLS);

        Texture2D icon = AssetDatabase.LoadAssetAtPath<Texture2D>("Assets/icon.png") as Texture2D;
        Texture2D[] icons = new Texture2D[] { icon };//, icon, icon, icon};
        PlayerSettings.SetIconsForTargetGroup(BuildTargetGroup.Unknown, icons);

        if (File.Exists("Assets/splash.png"))
        {
            Texture2D splash = AssetDatabase.LoadAssetAtPath<Texture2D>("Assets/splash.png") as Texture2D;
            PlayerSettings.SplashScreen.showUnityLogo = false;
            PlayerSettings.SplashScreen.animationMode = PlayerSettings.SplashScreen.AnimationMode.Static;
            PlayerSettings.SplashScreen.background = Sprite.Create(splash, new Rect(0, 0, splash.width, splash.height), Vector2.zero);
        }
        else
        {
            PlayerSettings.SplashScreen.show = false;
        }
        PlayerSettings.applicationIdentifier = PACKAGE_NAME;
        PlayerSettings.companyName = COMPANY_NAME;
        PlayerSettings.productName = PRODUCT_NAME;
        PlayerSettings.bundleVersion = VERSION_NAME;
        QualitySettings.antiAliasing = ANTI_ALIASING;
    }
    public static void ChangeAndroidSetting()
    {
        ChangePlayerSettings();

        PlayerSettings.SetArchitecture(BuildTargetGroup.Android, (int)AndroidArchitecture.ARM64);
        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);
        PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Android, PACKAGE_NAME);
        PlayerSettings.Android.bundleVersionCode = VERSION_CODE;
        PlayerSettings.Android.keystoreName = KEYSTORE_NAME;
        PlayerSettings.Android.keystorePass = KEYSTORE_PASS; // "tuyougame";
        PlayerSettings.Android.keyaliasName = KEYALIAS_NAME; // "dizhu";
        PlayerSettings.Android.keyaliasPass = KEYALIAS_PASS; // "tuyougame";

#if FISH_PORTRAIT
        PlayerSettings.defaultInterfaceOrientation = UIOrientation.Portrait;
        PlayerSettings.allowedAutorotateToPortrait = true;
        PlayerSettings.allowedAutorotateToLandscapeLeft = false;
        PlayerSettings.allowedAutorotateToLandscapeRight = false;
#else
        PlayerSettings.defaultInterfaceOrientation = UIOrientation.LandscapeLeft;
        PlayerSettings.allowedAutorotateToPortrait = true;
        PlayerSettings.allowedAutorotateToLandscapeLeft = true;
        PlayerSettings.allowedAutorotateToLandscapeRight = false;
#endif

#if UNITY_HW
        //////////////////////////ADD BY QIZHIBIN
        //兼容64位
        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);
        PlayerSettings.SetApiCompatibilityLevel(BuildTargetGroup.Android, ApiCompatibilityLevel.NET_Standard_2_0);
        AndroidArchitecture aac = AndroidArchitecture.ARM64 | AndroidArchitecture.ARMv7;
        PlayerSettings.Android.targetArchitectures = aac;
        PlayerSettings.Android.buildApkPerCpuArchitecture = false;

        //obb
        PlayerSettings.Android.useAPKExpansionFiles = false;
        //made with unity?
        PlayerSettings.SplashScreen.showUnityLogo = false;
#endif
    }
    public static void ChangeiOSSettings()
    {
        ChangePlayerSettings();
        PlayerSettings.SetArchitecture(BuildTargetGroup.iOS, 1);
        PlayerSettings.iOS.applicationDisplayName = PRODUCT_NAME;
        PlayerSettings.iOS.buildNumber = BUILD_NUMBER.ToString();
        PlayerSettings.iOS.appleDeveloperTeamID = APPLE_DEVELOPER_TEAM_ID;
        PlayerSettings.iOS.iOSManualProvisioningProfileID = PROVISIONING_PROFILE_ID;

    }
    public static void BuildAssembly()
    {
        Debug.Log("重新编译脚本");
        var scriptImporter = AssetImporter.GetAtPath("Assets/GameScript/Runtime/Global.cs");
        scriptImporter.SaveAndReimport();
    }
    private static void UpdateAssets()
    {
        // if (!DEFINE_SYMBOLS.Contains("FISH_PAY")) 
        // {
        //     AssetDatabase.DeleteAsset(pathAssetDatabase.GUIDToAssetPath("Assets/GameRes/UIPanel/Atlas/charge.png"));
        //     string[] unusedFolder = { "Assets/GameRes/UIPanel/Prefab/charge/", "Assets/Scripts/view/charge/"};
        //     foreach (var asset in AssetDatabase.FindAssets("", unusedFolder))
        //     {
        //         var path = AssetDatabase.GUIDToAssetPath(asset);
        //         AssetDatabase.DeleteAsset(path);
        //     }
        // }

#if FISH_MJ
		MajiaAssetsMappingTools.MappingResourcesAssets();
#endif

        AssetDatabase.DeleteAsset("Assets/Plugins/codeandweb.com");
        AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
    }
    private static void AppBuildPrepare()
    {
        bool prepareResult = AssetBuildPrepareTools.AppBuildPrepare();
        if (prepareResult == false)
            throw new System.Exception("Prepare to build app failed. See above error info.");
    }

    public static void ScanAll()
    {
        YooAsset.Editor.AssetArtScannerSettingData.ScanAll();
    }
}