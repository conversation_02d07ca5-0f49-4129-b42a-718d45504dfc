using System;
using UnityEngine;
using UnityEditor;
using System.Collections;
using UnityEditor.Animations;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using UnityEngine.SceneManagement;
using UnityEditor.SceneManagement;


public class ArtistTools : EditorWindow
{
    [MenuItem("Tools/美术常用的工具", false, 400)]
    public static void OpenWindow()
    {
        ArtistTools window = (ArtistTools)EditorWindow.GetWindow(typeof(ArtistTools));
        window.position = new Rect(Screen.width / 2 + 300, 400, 380, 300);

    }

void OnGUI()
    {
		
		//if (GUI.Button(new Rect(20, 20, 150, 30), "模型的导入设置\n (无动画)"))
  //      {
  //          SetModelImportSettingsNoAnima();
  //      }
        GUI.contentColor = new Color(0.7f, 1f, 0.7f, 1f);
        if ( GUI.Button(new Rect(20, 20, 150, 30), "模型的导入设置"))
        {
            SetModelImportSettings();
        }
        //GUI.contentColor = new Color(0.3f, 1.0f, 3.5f, 1f);
        //if (GUI.Button(new Rect(20, 60, 150, 30), "提取动画片段(压缩动画)"))
        //{
        //    ExtractAnimationClipFromModel();
        //}
        GUI.contentColor = new Color(0.3f, 1.0f, 3.5f, 1f);
        if (GUI.Button(new Rect(20, 60, 150, 30), "提取动画片段(不压缩)"))
        {
            ExtractAnimationClipFromModel02();
        }
        GUI.contentColor = new Color(0.3f, 1.0f, 3.5f, 1f);
        if (GUI.Button(new Rect(20, 100, 150, 30), "动画精度优化"))
        {
            OptimizeAnimationClip();
        }
        GUI.contentColor = new Color(0.7f, 1f, 0.7f, 1f);
        if (GUI.Button(new Rect(20, 140, 150, 30), "创建动画控制器"))
        {
            AnimatorTool();
        }

        if (GUI.Button(new Rect(20, 180, 150, 30), "移除无效粒子系统"))
        {
            RemoveInactiveRenderers();
        }

        GUI.contentColor = new Color(0.3f,1.0f, 3.5f, 1f);
        if (GUI.Button(new Rect(200, 20, 150, 30), "贴图格式(Texture)设置"))
		{
			FXTextureImportSetting();
        }
        GUI.contentColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
        if (GUI.Button(new Rect(200, 60, 150, 30), "批量制作预制体"))
        {
            BatchPrefab();
        }		
        if (GUI.Button(new Rect(200, 100, 150, 30), "Optimize预制体"))
        {
            OptimizeGameObject();
        }
        if (GUI.Button(new Rect(200, 140, 150, 30), "创建文件夹(无模型)"))
        {
            CreateFolderStructure();
        }
        if (GUI.Button(new Rect(200, 180, 150, 30), "创建空文件夹"))
        {
            CreateFolder02();
        }

        GUI.contentColor = new Color(0.3f, 1.0f, 3.5f, 1f);
        if (GUI.Button(new Rect(200, 220, 150, 30), "清理材质球冗余"))
        {
            ClearMaterial();
        }
        //if (GUI.Button(new Rect(380, 60, 150, 30), "旋转摄像机动画（未完成）"))
        //{
        //    ReturnCamera();
        //}


    }

    public AnimationClip mAnimationClip = null;
    public void ReturnCamera()
    {
        UnityEngine.Object[] objects = Selection.GetFiltered(typeof(AnimationClip), SelectionMode.DeepAssets);
        if (objects.Length <= 0)
        {

            return;
        }
        for (int i = 0; i < objects.Length; ++i)
        {
            Type t = objects[i].GetType();

            if (t != typeof(AnimationClip))
                continue;
            AnimationClip clip = objects[i] as AnimationClip;
            mAnimationClip = clip;
            break;
        }

        if (mAnimationClip == null)
        {
            EditorUtility.DisplayDialog("没选中Clip", "没选中Clip", "确定");

        }
        AnimationClipCurveData[] curves = AnimationUtility.GetAllCurves(mAnimationClip);
        bool HasChange = false;
        for (int j = 0; j < curves.Length; ++j)
        {
            var itemcurves = curves[j];
            Keyframe[] keys = itemcurves.curve.keys;

            for (int i = 0; i < keys.Length; ++i)
            {
                var Keyitem = keys[i];
                float tempvalue = Keyitem.value;
                float inputValue = EditorGUILayout.FloatField(Keyitem.value);

                if (tempvalue != inputValue)
                {
                    Keyitem.value = inputValue;
                    HasChange = true;
                    keys[i] = Keyitem;

                    Debug.Log("ValueChange");
                }
                GUILayout.EndHorizontal();
            }

            if (HasChange)
            {
                itemcurves.curve.keys = keys;
                mAnimationClip.SetCurve(itemcurves.path, itemcurves.type, itemcurves.propertyName, itemcurves.curve);
            }

        }

    }
    private static void CreateFolderStructure()
    {
        // 获取所有选中项的 GUID
        string[] selectedGUIDs = Selection.assetGUIDs;
        if (selectedGUIDs.Length == 0)
        {
            Debug.LogError("请先在 Project 窗口中选择一个文件夹！");
            return;
        }

        bool anyCreated = false;
        List<string> validFolders = new List<string>();

        // 阶段1：收集有效文件夹
        foreach (string guid in selectedGUIDs)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            if (AssetDatabase.IsValidFolder(path))
            {
                validFolders.Add(path);
            }
        }

        if (validFolders.Count == 0)
        {
            Debug.LogError("没有选中有效文件夹！");
            return;
        }

        // 阶段2：创建所有父级文件夹
        foreach (string folderPath in validFolders)
        {
            if (CreatePrimaryFolders(folderPath))
            {
                anyCreated = true;
            }
        }

        // 阶段3：创建子文件夹（需要父文件夹确认存在）
        if (anyCreated)
        {
            AssetDatabase.Refresh(); // 关键刷新点
        }

        foreach (string folderPath in validFolders)
        {
            CreateAnimSubfolders(folderPath);
        }

        AssetDatabase.Refresh();
    }
    private static bool CreatePrimaryFolders(string rootPath)
    {
        bool created = false;

        // 创建一级文件夹
        string[] mainFolders = { "anim", "texture", "material", "model", "prefab", "volume", "timeline" };
        foreach (string folder in mainFolders)
        {
            string newPath = Path.Combine(rootPath, folder);
            if (!AssetDatabase.IsValidFolder(newPath))
            {
                string error = AssetDatabase.CreateFolder(rootPath, folder);
                if (string.IsNullOrEmpty(error))
                {
                    Debug.Log($"已创建主文件夹：{newPath}");
                    created = true;
                }
            }
        }
        return created;
    }
    private static void CreateAnimSubfolders(string rootPath)
    {
        string animPath = Path.Combine(rootPath, "anim");

        // 严格验证父文件夹存在
        if (AssetDatabase.IsValidFolder(animPath))
        {
            string[] subFolders = { "anim_youchang", "anim_jilifankui", "anim_juexing01", "anim_juexing02" };
            foreach (string folder in subFolders)
            {
                string newPath = Path.Combine(animPath, folder);
                if (!AssetDatabase.IsValidFolder(newPath))
                {
                    string error = AssetDatabase.CreateFolder(animPath, folder);
                    if (string.IsNullOrEmpty(error))
                    {
                        Debug.Log($"已创建子文件夹：{newPath}");
                    }
                }
            }
        }
        else
        {
            Debug.LogError($"无法创建子文件夹，父级Anim不存在：{animPath}");
        }
    }

    private static void CreateFolder02()
    {
        UnityEngine.Object[] selections = Selection.GetFiltered(typeof(UnityEngine.Object), SelectionMode.DeepAssets);
        for (int i = 0; i < selections.Length; ++i)
        {
            GameObject g = selections[i] as GameObject;

            if (!g)
                continue;
            // 文件路径
            string modelPath = AssetDatabase.GetAssetPath(g);
            int nameSeparator = modelPath.LastIndexOf('/');
            string folderPathA = modelPath.Substring(0, nameSeparator) + "/anim";
            string folderPathAnim02 = Path.Combine(folderPathA, "anim_youchang");
            string folderPathAnim03 = Path.Combine(folderPathA, "anim_jilifankui");
            string folderPathAnim04 = Path.Combine(folderPathA, "anim_juexing01");
            string folderPathAnim05 = Path.Combine(folderPathA, "anim_juexing02");

            string folderPathT = modelPath.Substring(0, nameSeparator) + "/texture";
            string folderPathM = modelPath.Substring(0, nameSeparator) + "/material";
            string folderPathModel = modelPath.Substring(0, nameSeparator) + "/model";
            string folderPathP = modelPath.Substring(0, nameSeparator) + "/prefab";
            string folderPathV = modelPath.Substring(0, nameSeparator) + "/volume";
            string folderPatht = modelPath.Substring(0, nameSeparator) + "/timeline";

            if (!Directory.Exists(folderPathA) || !Directory.Exists(folderPathT) || !Directory.Exists(folderPathM)
                || !Directory.Exists(folderPathP) || !Directory.Exists(folderPathV) || !Directory.Exists(folderPatht) || !Directory.Exists(folderPathModel)
                || !Directory.Exists(folderPathAnim02) || !Directory.Exists(folderPathAnim03) || !Directory.Exists(folderPathAnim04) || !Directory.Exists(folderPathAnim05))
            {
                Directory.CreateDirectory(folderPathA);
                Directory.CreateDirectory(folderPathT);
                Directory.CreateDirectory(folderPathM);
                Directory.CreateDirectory(folderPathP);
                Directory.CreateDirectory(folderPathV);
                Directory.CreateDirectory(folderPatht);
                Directory.CreateDirectory(folderPathModel);
                Directory.CreateDirectory(folderPathAnim02);
                Directory.CreateDirectory(folderPathAnim03);
                Directory.CreateDirectory(folderPathAnim04);
                Directory.CreateDirectory(folderPathAnim05);
                Debug.Log("创建了完成");
            }
        }
        // 刷新
        EditorUtility.ClearProgressBar();
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }

    private const float AnimationpositionError = 0.0f;
    private const float AnimationRotationError = 0.0f;
    private const float AnimationScaleError = 0.0f;

    //    public static void SetModelImportSettingsNoAnima()
    //    {
    //        UnityEngine.Object[] selections = Selection.GetFiltered(typeof(UnityEngine.Object), SelectionMode.DeepAssets);

    //        for (int i = 0; i < selections.Length; ++i)
    //        {
    //            GameObject g = selections[i] as GameObject;
    //            if (!g)
    //                continue;
    //            GameObject mod = g as GameObject;
    //            string modelPath = AssetDatabase.GetAssetPath(mod);
    //            EditorUtility.DisplayProgressBar("Set Model Import Settings", g.name, (float)i / selections.Length);
    //            ModelImporter mi = AssetImporter.GetAtPath(modelPath) as ModelImporter;
    //            mi.animationType = ModelImporterAnimationType.None;
    //            //  特殊处理一下skin文件
    //            if (g.name.Contains("skin") || g.name.Contains("Skin"))
    //            {
    //                mi.animationType = ModelImporterAnimationType.Generic;
    //            }
    //            mi.importAnimation = false;
    //            mi.isReadable = false;
    //            mi.importBlendShapes = false;
    //            mi.importVisibility = false;
    //#if UNITY_2019_1_OR_NEWER
    //            mi.materialImportMode = ModelImporterMaterialImportMode.None;
    //#else
    //            mi.importMaterials = false;
    //#endif
    //            mi.importCameras = false;
    //            mi.importLights = false;
    //            mi.addCollider = false;
    //            mi.keepQuads = false;
    //            mi.weldVertices = false;
    //            mi.swapUVChannels = false;
    //            mi.generateSecondaryUV = false;
    //            mi.SaveAndReimport();
    //            Debug.Log("设置完成");
    //        }
    //        EditorUtility.ClearProgressBar();
    //    }
    static Material ModelDefaultMaterial
    {
        get
        {
            var ttt = AssetDatabase.LoadAssetAtPath<Material>("Assets/Resources/temp/ModelDefaultMaterial.mat");
            return ttt;
        }
    }
    public static void CheckMat()
    {

        var selectObject = Selection.objects[0];
        if (selectObject != null && selectObject is GameObject)
        {
            string modelPath = AssetDatabase.GetAssetPath(selectObject);
            ModelImporter mi = AssetImporter.GetAtPath(modelPath) as ModelImporter;
            if (mi != null)
            {


                AssetImporter.SourceAssetIdentifier key = new AssetImporter.SourceAssetIdentifier();
                bool hasNoName = false;
                foreach (var iii in mi.GetExternalObjectMap())
                {
                    if (iii.Key.name == "No Name")
                    {
                        hasNoName = true;
                        key = iii.Key;
                        break;
                    }
                }

                if (hasNoName)
                {
                    mi.RemoveRemap(key);
                    mi.AddRemap(key, ModelDefaultMaterial);
                }


            }
        }
    }
    public static void SetModelImportSettings()
    {

        UnityEngine.Object[] selections = Selection.GetFiltered(typeof(GameObject), SelectionMode.DeepAssets);

        for (int i = 0; i < selections.Length; ++i)
        {
            GameObject g = selections[i] as GameObject;
            if (!g)
                continue;
            GameObject mod = g as GameObject;
            string modelPath = AssetDatabase.GetAssetPath(mod);

            EditorUtility.DisplayProgressBar("Set Model Import Settings", g.name, (float)i / selections.Length);
            ModelImporter mi = AssetImporter.GetAtPath(modelPath) as ModelImporter;
            mi.animationType = ModelImporterAnimationType.Generic;
            mi.optimizeGameObjects = false;
            #if UNITY_2019_1_OR_NEWER
                mi.avatarSetup = ModelImporterAvatarSetup.CreateFromThisModel; // unity 2018版本不支持 2019可用
            #endif
            mi.isReadable = false;
            mi.importBlendShapes = false;
            mi.importVisibility = false;
            mi.importCameras = false;
            mi.importLights = false;
            mi.importAnimation = true;
            mi.addCollider = false;
            mi.keepQuads = false;
            mi.weldVertices = false;
            mi.swapUVChannels = false;
            mi.generateSecondaryUV = false;
            if (modelPath.Contains("/Effect") || modelPath.Contains("/../Effect"))
            {
                mi.importAnimation = false;
                mi.isReadable = true;
            }
            #if UNITY_2019_1_OR_NEWER
                mi.materialImportMode = ModelImporterMaterialImportMode.None;
            #else
                mi.importMaterials = true;
                mi.useSRGBMaterialColor = true;
                mi.materialLocation = ModelImporterMaterialLocation.InPrefab;
                //CheckMat();
                var sources = new List<Renderer>();
                var assets = AssetDatabase.LoadAllAssetsAtPath(mi.assetPath);
                for (var ii = 0; ii < assets.Length; ii++)
                {
                    var source = assets[ii] as Renderer;
                    if (source != null)
                    {
                        sources.Add(source);
                    }
                }
                //所有material
                var keys = new Dictionary<string, bool>();
                foreach (var render in sources)
                {
                    foreach (var mat in render.sharedMaterials)
                    {
                        if (mat != null && !keys.ContainsKey(mat.name) && mat.shader.name.Contains("Standard"))
                            keys.Add(mat.name, true);
                    }
                }
                //替换为material              
                var newMaterial = AssetDatabase.LoadAssetAtPath<Material>("Assets/GameArt/DefaultAsset/ModelDefaultMaterial.mat");
                var kind = typeof(UnityEngine.Material);
                foreach (var iii in keys)
                {
                    AssetImporter.SourceAssetIdentifier id = new AssetImporter.SourceAssetIdentifier();
                    id.name = iii.Key;
                    id.type = kind;
                    mi.RemoveRemap(id);
                    mi.AddRemap(id, newMaterial);
                }
            #endif

            mi.animationCompression = ModelImporterAnimationCompression.KeyframeReduction;
            //mi.animationCompression = ModelImporterAnimationCompression.Optimal;
            //mi.animationPositionError = 0.5f;
            //mi.animationRotationError = 0.5f;
            //mi.animationScaleError = 0.5f;
            if (g.name.Contains("fx_"))
            {
                mi.isReadable = true;
            }
            if (g.name.Contains("camera") || g.name.Contains("Camera"))
            {
                mi.importCameras = true;
                mi.animationCompression = ModelImporterAnimationCompression.Off;
            }
            if (g.name.Contains("skin") || g.name.Contains("Skin") || g.name.Contains("obj"))
            {
                mi.importAnimation = false;
                Debug.Log(g.name + "是基础骨骼文件,动画设置为空");
            }
            mi.SaveAndReimport();
        }
        EditorUtility.ClearProgressBar();
    }

    // 压缩动画精度 动画会出现轻微摆动 建议移动中的动画使用压缩方式 待机动画不推荐
    static void OptimizeAnimationClip()
    {
        UnityEngine.Object[] objects = Selection.GetFiltered(typeof(AnimationClip), SelectionMode.DeepAssets);
        long totalOriginFileSize = 0;
        long totalNewFileSize = 0;
        int totalOriginMemSize = 0;
        int totalNewMemSize = 0;

        Assembly asm = Assembly.GetAssembly(typeof(Editor));
        MethodInfo getAnimationClipStats = typeof(AnimationUtility).GetMethod("GetAnimationClipStats", BindingFlags.Static | BindingFlags.NonPublic);
        System.Type clipStats = asm.GetType("UnityEditor.AnimationClipStats");
        FieldInfo sizeField = clipStats.GetField("size", BindingFlags.Public | BindingFlags.Instance);

        for (int i = 0; i < objects.Length; ++i)
        {
            AnimationClip clip = objects[i] as AnimationClip;
            if (!clip)
                continue;

            EditorUtility.DisplayProgressBar("Optimizing Animation Clips", clip.name, (float)i / objects.Length);
            string clipPath = AssetDatabase.GetAssetPath(clip);
            totalOriginFileSize += new FileInfo(clipPath).Length;
            totalOriginMemSize += (int)sizeField.GetValue(getAnimationClipStats.Invoke(null, new object[] { clip }));

            OptimizeAnimationClipRemoveScale(clip);
            AssetDatabase.SaveAssets();

            totalNewFileSize += new FileInfo(clipPath).Length;
            sizeField.GetValue(getAnimationClipStats.Invoke(null, new object[] { AssetDatabase.LoadAssetAtPath<AnimationClip>(clipPath) }));//这一次调用的结果是0,下一次调用才正确，不知道为啥
            totalNewMemSize += (int)sizeField.GetValue(getAnimationClipStats.Invoke(null, new object[] { AssetDatabase.LoadAssetAtPath<AnimationClip>(clipPath) }));
        }
        EditorUtility.ClearProgressBar();
        Debug.Log(string.Format("Optimize Finish. FileSize {0} -> {1}, MemSize {2} -> {3}", EditorUtility.FormatBytes(totalOriginFileSize), EditorUtility.FormatBytes(totalNewFileSize),
        EditorUtility.FormatBytes(totalOriginMemSize), EditorUtility.FormatBytes(totalNewMemSize)));


    }
    //  remove animation scale
    public static void OptimizeAnimationClipRemoveScale(AnimationClip clip, int input = 3)
    {
        //Remove Scale
        AnimationClipCurveData[] curves = null;
        curves = AnimationUtility.GetAllCurves(clip);
        Keyframe[] keyframes;
        List<string> nonDelete = new List<string>();
        if (curves != null && curves.Length != 0)
        {
            double beginKeyValue = 0f;
            for (int i = 0; i < curves.Length; i++)
            {
                AnimationClipCurveData curveDate = curves[i];
                if (curveDate.curve == null || curveDate.curve.keys == null)
                {
                    continue;
                }
                if (curveDate.propertyName.ToLower().Contains("scale"))
                {
                    keyframes = curveDate.curve.keys;
                    beginKeyValue = Math.Round(keyframes[0].value,3);
                    for (int j = 1; j < keyframes.Length; j++)
                    {
                        if (beginKeyValue != Math.Round(keyframes[j].value, 3))
                        {
                            nonDelete.Add(curveDate.path);
                            continue;
                            
                        }                      
                    }
                }               
            }
           
        }
        foreach (EditorCurveBinding theCurvesBinding in AnimationUtility.GetCurveBindings(clip))
        {
            if (!nonDelete.Contains(theCurvesBinding.path) && theCurvesBinding.propertyName.ToLower().Contains("scale"))
            {
                //Debug.Log("裁减掉" + theCurvesBinding.path);
                AnimationUtility.SetEditorCurve(clip, theCurvesBinding, null);
            }
        }



        string len = "f" + input;
        //Optimize Accuracy
        AnimationClipCurveData[] curves02 = AnimationUtility.GetAllCurves(clip);
        for (int j = 0; j < curves02.Length; ++j)
        {
            AnimationClipCurveData curve = curves02[j];
            if (null == curve || curve.curve.keys == null)
                continue;

            Keyframe[] frames = curve.curve.keys;
            for (int k = 0; k < frames.Length; ++k)
            {
                Keyframe frame = frames[k];
                frame.value = float.Parse(frame.value.ToString("f3"));
                frame.inTangent = float.Parse(frame.inTangent.ToString("f3"));
                frame.outTangent = float.Parse(frame.outTangent.ToString("f3"));
                frames[k] = frame;
            }
            curve.curve.keys = frames;
            clip.SetCurve(curve.path, curve.type, curve.propertyName, curve.curve);
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();//刷新unity资源显示
    }


    public static void BatchPrefab()
    {
		string path = Application.dataPath + "/";
		Directory.CreateDirectory(path + "APrefabs");
		
        Transform tParent = ((GameObject)Selection.activeObject).transform;
        UnityEngine.Object tempPrefab;
        int i = 0;
        foreach (Transform t in tParent)
        {
            tempPrefab = PrefabUtility.CreateEmptyPrefab("Assets/APrefabs/" + t.name + ".prefab");
            tempPrefab = PrefabUtility.ReplacePrefab(t.gameObject, tempPrefab);
            i++;
        }
    }

    //  提取模型动画片段  压缩动画 删scale
    public static void ExtractAnimationClipFromModel()
    {

        UnityEngine.Object[] selections = Selection.GetFiltered(typeof(UnityEngine.Object), SelectionMode.DeepAssets);

        for (int i = 0; i < selections.Length; ++i)
        {
            GameObject g = selections[i] as GameObject;
            if (!g)
                continue;

            string modelPath = AssetDatabase.GetAssetPath(g);
            ModelImporter mi = AssetImporter.GetAtPath(modelPath) as ModelImporter;
            if (!mi || !mi.importAnimation)
                continue;

            EditorUtility.DisplayProgressBar("Extra Animation Clips", mi.name, (float)i / selections.Length);
            mi.animationType = ModelImporterAnimationType.Generic;
            mi.SaveAndReimport();

            //int namefolderPath = modelPath.LastIndexOf('/');
            //string folderPath = modelPath.Substring(0, namefolderPath) + "/../Anim/";
            string folderPath = modelPath.Substring(0, modelPath.LastIndexOf('/'));
            folderPath = folderPath.Substring(0, folderPath.LastIndexOf('/')) + "/Anim/"; 
            if (!Directory.Exists(folderPath))
                Directory.CreateDirectory(folderPath);
            if (!Directory.Exists(folderPath))
                Directory.CreateDirectory(folderPath);
            //string newName = "Anim";
            //folderPath = newName;

            UnityEngine.Object[] assets = AssetDatabase.LoadAllAssetsAtPath(modelPath);
            for (int j = 0; j < assets.Length; ++j)
            {
                AnimationClip clip = assets[j] as AnimationClip;
                if (!clip || clip.name.Contains("preview") )
                    continue;
                if (clip.name.Contains("loop") || clip.name.Contains("Loop") || clip.name.Contains("idle") || clip.name.Contains("Idle"))
                {
                    AnimationClipSettings clipSetting = AnimationUtility.GetAnimationClipSettings(clip);
                    clipSetting.loopTime = true;
                    AnimationUtility.SetAnimationClipSettings(clip, clipSetting);
                }
                OptimizeAnimationClipRemoveScale(clip);
                OptimizeAnimationClip();
                Debug.Log("动画已经优化，如果动画出现明显晃动接受不了，建议使用无压缩模式");
                AnimationClip newClip = new AnimationClip();
                EditorUtility.CopySerialized(clip, newClip);
                AssetDatabase.CreateAsset(newClip, folderPath + clip.name + ".anim");
            }
        }
        EditorUtility.ClearProgressBar();
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

    }

    //  提取模型动画片段 不删除scale 不优化动画精度
    public static void ExtractAnimationClipFromModel02()
    {
        UnityEngine.Object[] selections = Selection.GetFiltered(typeof(UnityEngine.Object), SelectionMode.DeepAssets);
        for (int i = 0; i < selections.Length; ++i)
        {
            GameObject g = selections[i] as GameObject;
            if (!g)
                continue;

            string modelPath = AssetDatabase.GetAssetPath(g);
            ModelImporter mi = AssetImporter.GetAtPath(modelPath) as ModelImporter;
            if (!mi || !mi.importAnimation)
                continue;

            EditorUtility.DisplayProgressBar("Extra Animation Clips", mi.name, (float)i / selections.Length);
            mi.animationType = ModelImporterAnimationType.Generic;
            // mi.animationCompression = ModelImporterAnimationCompression.Optimal;
            mi.SaveAndReimport();

            //string folderPath = modelPath.Substring(0, modelPath.LastIndexOf('/'));
            //folderPath += "/Anim/";
            string folderPath = modelPath.Substring(0, modelPath.LastIndexOf('/'));
            folderPath = folderPath.Substring(0, folderPath.LastIndexOf('/')) + "/Anim/";
            if (!Directory.Exists(folderPath))
                Directory.CreateDirectory(folderPath);

            UnityEngine.Object[] assets = AssetDatabase.LoadAllAssetsAtPath(modelPath);
            for (int j = 0; j < assets.Length; ++j)
            {
                AnimationClip clip = assets[j] as AnimationClip;
                if (!clip || clip.name.Contains("preview"))
                    continue;
                if (clip.name.Contains("loop") || clip.name.Contains("idle") )
                {
                    AnimationClipSettings clipSetting = AnimationUtility.GetAnimationClipSettings(clip);
                    clipSetting.loopTime = true;
                    AnimationUtility.SetAnimationClipSettings(clip, clipSetting);
                }
                AnimationClip newClip = new AnimationClip();
                EditorUtility.CopySerialized(clip, newClip);
                AssetDatabase.CreateAsset(newClip, folderPath + clip.name + ".anim");
            }

            // 3. 准备新旧动画映射表（名称小写为Key）
            Dictionary<string, AnimationClip> newClips = BuildClipDictionary(folderPath);
            // 4. 查找所有需要处理的控制器
            AnimatorController[] controllers = FindAnimControllers(folderPath);
            // 5. 执行核心重定向逻辑
            foreach (var controller in controllers)
            {
                if (ProcessController(controller, newClips))
                {
                    Debug.Log($"成功更新控制器：{controller.name}");
                    EditorUtility.SetDirty(controller);
                }
            }

            AssetDatabase.SaveAssets();
            //EditorUtility.DisplayDialog("完成", "动画重定向完成！请检查控制器引用", "确定");
        }
        EditorUtility.ClearProgressBar();
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }

    /// <summary>
    /// 构建动画名称词典（Key为小写名称）
    /// </summary>
    private static Dictionary<string, AnimationClip> BuildClipDictionary(string folderPath)
    {
        Dictionary<string, AnimationClip> clipMap = new Dictionary<string, AnimationClip>();

        foreach (string guid in AssetDatabase.FindAssets("t:AnimationClip", new[] { folderPath }))
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            AnimationClip clip = AssetDatabase.LoadAssetAtPath<AnimationClip>(path);
            if (clip != null)
            {
                string key = clip.name.ToLower().Trim();
                if (!clipMap.ContainsKey(key))
                {
                    clipMap.Add(key, clip);
                }
            }
        }
        return clipMap;
    }

    /// <summary>
    /// 查找指定目录的所有动画控制器
    /// </summary>
    private static AnimatorController[] FindAnimControllers(string folderPath)
    {
        List<AnimatorController> controllers = new List<AnimatorController>();

        foreach (string guid in AssetDatabase.FindAssets("t:AnimatorController", new[] { folderPath }))
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            AnimatorController controller = AssetDatabase.LoadAssetAtPath<AnimatorController>(path);
            if (controller != null)
            {
                controllers.Add(controller);
            }
        }
        return controllers.ToArray();
    }

    /// <summary>
    /// 处理单个控制器的核心逻辑
    /// </summary>
    private static bool ProcessController(AnimatorController controller, Dictionary<string, AnimationClip> clipMap)
    {
        bool modified = false;

        // 遍历所有层
        foreach (AnimatorControllerLayer layer in controller.layers)
        {
            modified |= ProcessStateMachine(layer.stateMachine, clipMap);
        }
        return modified;
    }

    /// <summary>
    /// 递归处理状态机结构
    /// </summary>
    private static bool ProcessStateMachine(AnimatorStateMachine stateMachine, Dictionary<string, AnimationClip> clipMap)
    {
        bool modified = false;

        // 处理普通状态
        foreach (ChildAnimatorState state in stateMachine.states)
        {
            modified |= UpdateStateMotion(state.state, clipMap);
        }

        // 处理混合树
        foreach (ChildAnimatorState state in stateMachine.states)
        {
            if (state.state.motion is BlendTree blendTree)
            {
                modified |= ProcessBlendTree(blendTree, clipMap);
            }
        }

        // 递归处理子状态机
        foreach (ChildAnimatorStateMachine childMachine in stateMachine.stateMachines)
        {
            modified |= ProcessStateMachine(childMachine.stateMachine, clipMap);
        }

        return modified;
    }

    /// <summary>
    /// 更新单个状态的动画引用
    /// </summary>
    private static bool UpdateStateMotion(AnimatorState state, Dictionary<string, AnimationClip> clipMap)
    {
        if (state.motion == null) return false;

        string clipKey = state.motion.name.ToLower().Trim();
        if (clipMap.TryGetValue(clipKey, out AnimationClip newClip))
        {
            // 使用GUID对比确保物理文件不同
            string oldGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(state.motion));
            string newGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(newClip));

            if (oldGuid != newGuid)
            {
                state.motion = newClip;
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 处理混合树的嵌套结构
    /// </summary>
    private static bool ProcessBlendTree(BlendTree blendTree, Dictionary<string, AnimationClip> clipMap)
    {
        bool modified = false;
        ChildMotion[] children = blendTree.children;

        for (int i = 0; i < children.Length; i++)
        {
            ChildMotion child = children[i];
            if (child.motion == null) continue;

            // 处理普通动画
            string clipKey = child.motion.name.ToLower().Trim();
            if (clipMap.TryGetValue(clipKey, out AnimationClip newClip))
            {
                string oldGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(child.motion));
                string newGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(newClip));

                if (oldGuid != newGuid)
                {
                    child.motion = newClip;
                    children[i] = child;
                    modified = true;
                }
            }

            // 递归处理嵌套混合树
            if (child.motion is BlendTree subTree)
            {
                modified |= ProcessBlendTree(subTree, clipMap);
            }
        }

        if (modified)
        {
            blendTree.children = children;
        }
        return modified;
    }
    public static void OptimizeGameObject()
    {
        UnityEngine.Object[] selections = Selection.GetFiltered(typeof(GameObject), SelectionMode.Assets);
        if (null == selections || selections.Length == 0)
            return;

        for (int i = 0; i < selections.Length; ++i)
        {
            GameObject g = selections[i] as GameObject;
            if (!g)
                continue;

            GameObject fish = GameObject.Instantiate(g) as GameObject;
            if (!fish.GetComponent<Animator>())
                continue;

            fish.name = g.name;
            Dictionary<Transform, List<GameObject>> exposedTransforms = new Dictionary<Transform, List<GameObject>>();
            TrackTransform(exposedTransforms, fish.transform);

            SkinnedMeshRenderer smr = fish.GetComponentInChildren<SkinnedMeshRenderer>();
            //var dummys = fish.GetComponentInChildren<FishGoldExplodeNode>();
            if (!smr)
            {
                Debug.LogError("Skin is null " + fish.name, g);
                continue;
            }

            string modelPath = AssetDatabase.GetAssetPath(smr.sharedMesh);
            ModelImporter mi = AssetImporter.GetAtPath(modelPath) as ModelImporter;
            if (!mi)
            {
                Debug.LogError("ModelImporter Not Found! " + g.name, g);
                continue;
            }
            mi.optimizeGameObjects = true;
            if (exposedTransforms.Count > 0)
            {
                string[] tPaths = new string[exposedTransforms.Count];
                Dictionary<Transform, List<GameObject>>.Enumerator itr = exposedTransforms.GetEnumerator();
                int j = 0;
                while (itr.MoveNext())
                {
                    tPaths[j] = AnimationUtility.CalculateTransformPath(itr.Current.Key, fish.transform);
                    ++j;
                }
                mi.extraExposedTransformPaths = tPaths;
            }
            mi.SaveAndReimport();

            GameObject newPrefab = GameObject.Instantiate(AssetDatabase.LoadAssetAtPath(modelPath, typeof(GameObject))) as GameObject;
            newPrefab.name = fish.name;
            newPrefab.GetComponentInChildren<SkinnedMeshRenderer>().sharedMaterials = fish.GetComponentInChildren<SkinnedMeshRenderer>().sharedMaterials;
            Animator animator = newPrefab.GetComponent<Animator>();
            animator.cullingMode = AnimatorCullingMode.CullUpdateTransforms;
            animator.runtimeAnimatorController = fish.GetComponent<Animator>().runtimeAnimatorController;

            Dictionary<Transform, List<GameObject>>.Enumerator hangItr = exposedTransforms.GetEnumerator();
            while (hangItr.MoveNext())
            {
                List<GameObject> hangList = hangItr.Current.Value;
                string pointName = hangItr.Current.Key.name;
                Transform hangPoint = null;
                if (pointName == newPrefab.name)
                    hangPoint = newPrefab.transform;
                else
                    hangPoint = newPrefab.transform.Find(pointName);
                if (!hangPoint)
                {
                    Debug.LogError("优化模型上未找到挂点 " + pointName);
                    continue;
                }

                for (int h = 0; h < hangList.Count; ++h)
                {
                    if (hangList[h].GetComponent<SkinnedMeshRenderer>())
                        continue;

                    GameObject hangObj = Instantiate(hangList[h]) as GameObject;
                    hangObj.name = hangList[h].name;
                    hangObj.transform.parent = hangPoint;
                    hangObj.transform.localPosition = hangList[h].transform.localPosition;
                    hangObj.transform.localRotation = hangList[h].transform.localRotation;
                    hangObj.transform.localScale = hangList[h].transform.localScale;
                }
            }

            string originPath = AssetDatabase.GetAssetPath(g);
            int nameSeparator = originPath.LastIndexOf('/');
            string folderPath = originPath.Substring(0, nameSeparator);
            nameSeparator = folderPath.LastIndexOf('/');
            folderPath = folderPath.Substring(0, nameSeparator) + "/Optimized Prefabs";
            if (!Directory.Exists(folderPath))
                Directory.CreateDirectory(folderPath);

            PrefabUtility.CreatePrefab(folderPath + '/' + newPrefab.name + ".prefab", newPrefab);
            DestroyImmediate(fish);
            DestroyImmediate(newPrefab);
        }
            AssetDatabase.Refresh();
        }
  

    public static void FXTextureImportSetting()
    {
        UnityEngine.Object[] textures = Selection.GetFiltered(typeof(Texture2D), SelectionMode.DeepAssets);

        foreach (Texture2D texture in textures)
        {
            string path = AssetDatabase.GetAssetPath(texture);
            TextureImporter texImporter = AssetImporter.GetAtPath(path) as TextureImporter;
			
            texImporter.anisoLevel = 1;
            texImporter.isReadable = false;
            texImporter.mipmapEnabled = false;
			//texImporter.alphaIsTransparency = true;
            texImporter.sRGBTexture = true;
            
            string texName = texture.name;
            //if (texImporter.maxTextureSize > 1024)
            //{
            //    texImporter.maxTextureSize = 1024;
            //}
            int maxSize = texImporter.maxTextureSize;

            TextureImporterFormat texFormat;
            if (texName.Contains("_ddn") || texName.Contains("_Normal"))
            {
                texImporter.textureType = TextureImporterType.NormalMap;
                texImporter.mipmapEnabled = false;
                texImporter.sRGBTexture = false;
            }
            if (texName.Contains("_maer") || texName.Contains("_rmas") || texName.Contains("_decal") || texName.Contains("_ilm") || texName.Contains("LUT") || texName.Contains("lut"))
            {
                texImporter.sRGBTexture = false;
                //texImporter.alphaIsTransparency = false;
            }

            if (texImporter.textureType == TextureImporterType.Default || texImporter.textureType == TextureImporterType.Sprite || texImporter.textureType == TextureImporterType.GUI)
            {
                
                if (texImporter.DoesSourceTextureHaveAlpha())
				{
                    texImporter.GetPlatformTextureSettings("Android", out maxSize, out texFormat);
                    texImporter.SetPlatformTextureSettings("Android", maxSize, TextureImporterFormat.ASTC_6x6);
                    //TextureImporterPlatformSettings platformSettings = new TextureImporterPlatformSettings();
                    //platformSettings.name = "Android";
                    //platformSettings.maxTextureSize = maxSize; // 调整为你需要的大小
                    //platformSettings.format = TextureImporterFormat.ASTC_4x4;
                    texImporter.GetPlatformTextureSettings("iPhone", out maxSize, out texFormat);
                    texImporter.SetPlatformTextureSettings("iPhone", maxSize, TextureImporterFormat.ASTC_6x6);
                    //texImporter.alphaIsTransparency = true;
                }
                else
                {
                    texImporter.GetPlatformTextureSettings("Android", out maxSize, out texFormat);
                    texImporter.SetPlatformTextureSettings("Android", maxSize, TextureImporterFormat.ASTC_6x6);
                    texImporter.GetPlatformTextureSettings("iPhone", out maxSize, out texFormat);
                    texImporter.SetPlatformTextureSettings("iPhone", maxSize, TextureImporterFormat.ASTC_6x6);
                    //texImporter.alphaIsTransparency = false;
                }
                AssetDatabase.ImportAsset(path);
            }
			else
			{
                //  如果贴图是法线贴图 统一设置成ASTC格式 Normalmap 
                texImporter.GetPlatformTextureSettings("Android", out maxSize, out texFormat);
				texImporter.SetPlatformTextureSettings("Android", maxSize, TextureImporterFormat.ASTC_4x4);
				texImporter.GetPlatformTextureSettings("iPhone", out maxSize, out texFormat);
				texImporter.SetPlatformTextureSettings("iPhone", maxSize, TextureImporterFormat.ASTC_4x4);
				AssetDatabase.ImportAsset(path);
			}
        }
    }

    /// <summary>
    /// 获取选择的贴图
    /// </summary>
    /// <returns></returns>
    private UnityEngine.Object[] GetSelectedTextures()
    {
        return Selection.GetFiltered(typeof(Texture2D), SelectionMode.DeepAssets);
    }
	
    static void TrackTransform(Dictionary<Transform, List<GameObject>> hangPoints, Transform parent)
    {
		for (int i = 0; i < parent.childCount; ++i)
		{
			Transform t = parent.GetChild(i);
			if (t.name.StartsWith("Bip") || t.name.StartsWith("Bone") || t.name.StartsWith("Dummy001") )
			{
				TrackTransform(hangPoints, t);
				continue;
			}

			List<GameObject> hangObjects;
			if (!hangPoints.TryGetValue(parent, out hangObjects))
			{
				hangObjects = new List<GameObject>();
				hangPoints.Add(parent, hangObjects);
			}
			hangObjects.Add(t.gameObject);
		}
    }
	 public static void PrefabApply()
    {
        Scene scene = SceneManager.GetActiveScene();
        foreach (GameObject go in scene.GetRootGameObjects())
        {
            ApplyByRoot(go);
        }
        EditorSceneManager.SaveScene(EditorSceneManager.GetActiveScene());
    }
    private static void ApplyByRoot(GameObject root)
    {  
        
        var prefab = UnityEditor.PrefabUtility.GetCorrespondingObjectFromSource(root);
        if (prefab != null)
        {
            string type = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(root);
            PrefabUtility.SaveAsPrefabAssetAndConnect(root, type, InteractionMode.UserAction);
        }
        else
        {
            for (int i = 0; i < root.transform.childCount; i++)
            {
                ApplyByRoot(root.transform.GetChild(i).gameObject);
            }
        }
    }


    //  创建动画控制器
    public static void AnimatorTool()
    {
        UnityEngine.Object[] objects = Selection.GetFiltered(typeof(AnimationClip), SelectionMode.DeepAssets);

        string patch = AssetDatabase.GetAssetPath(objects[0]);
        Dictionary<string, AnimatorState> stateDic = new Dictionary<string, AnimatorState>();
        List<string> all = new List<string>();
        List<AnimationClip> clips = new List<AnimationClip>();
        int a2 = patch.LastIndexOf("/");
        string name = patch.Substring(0, a2 + 1);

        string filename = "";
        string[] strArr = patch.Split('/');
        if (strArr.Length > 2)
        {
            filename = strArr[strArr.Length - 3];
        }

        name += filename + "_controller.controller";
        AnimatorController AnimatorCtrl = AnimatorController.CreateAnimatorControllerAtPath(name);
        AnimatorControllerLayer layer = AnimatorCtrl.layers[0];
        stateDic.Clear();
        for (int i = 0; i < objects.Length; i++)
        {
           
            {
                AnimationClip clip = objects[i] as AnimationClip;
                if ((null != clip) && (!clip.name.Contains("Take")))
                {
                    AnimationClip anim = (AnimationClip)clip;
                    AnimatorState state = layer.stateMachine.AddState(anim.name, new Vector3(260, (i - (objects.Length >> 1)) * 65, 0));
                    stateDic[anim.name] = state;
                    all.Add(anim.name);
                    clips.Add(anim);
                    state.motion = anim;
                }
            }
        }
        AnimatorStateMachine machine = layer.stateMachine;
        if (stateDic.ContainsKey("idle"))
        {
            machine.defaultState = stateDic["idle"];
        }
        
        for (int i = 0; i < all.Count; i++)
        {
            Dictionary<string, AnimatorState>.Enumerator itr = stateDic.GetEnumerator();
            while (itr.MoveNext())
            {
                string keyName = itr.Current.Key;
                if (("idle" == keyName)  )
                {
                    AnimatorStateTransition Trans = stateDic[keyName].AddTransition(stateDic["idle"]);                 
                    Trans.hasExitTime = true;
                    Trans.exitTime = 1;
                    Trans.duration = 0.0f;
                    Trans.conditions = null;
 
                }
            }
            
        }
        // 判断翅膀路径 设置默认动画是stand2 
        if (patch.Contains("/wings") || patch.Contains("/../wings"))
        {
            machine.defaultState = stateDic["stand2"];
            AnimatorStateTransition Trans = stateDic["show"].AddTransition(stateDic["stand2"]);
            Trans.hasExitTime = true;
            Trans.exitTime = 1;
            Trans.duration = 0.0f;
            Trans.conditions = null;
        }

    }





    private static Texture2D[] objectTextures;
    private static Material objectMaterial;
    private static Color objectColor;

    private static void Initialize()
    {
        // create a new texture and set it to red
        objectTextures = new Texture2D[1];
        objectTextures[0] = new Texture2D(1, 1);
        objectTextures[0].SetPixel(0, 0, Color.red);
        objectTextures[0].Apply();

        // create a new material and set its main texture to the newly created texture
        // objectMaterial = new Material(Shader.Find("Standard"));
        // objectMaterial.SetTexture("_MainTex", objectTextures[0]);
        objectMaterial = new Material(Shader.Find("CustomStandardV3"));
        objectMaterial.SetTexture("_Albedo", objectTextures[0]);

        // set the object's material to the newly created material
        SetObjectMaterial(objectMaterial);
    }
    private static void SetObjectMaterial(Material material)
    {
        // set the object's material
        objectMaterial = material;
    }
    private static void SetObjectColor(Color color)
    {
        // set the object's color
        objectColor = color;
    }
    private static void AdjustTextureColor()
    {
        // get RGB values from object's color
        float redValue = objectColor.r;
        float greenValue = objectColor.g;
        float blueValue = objectColor.b;

        // create new adjusted texture from original texture
        Texture2D oldTexture = objectTextures[0];
        Texture2D adjustedTexture = new Texture2D(oldTexture.width, oldTexture.height, oldTexture.format, false);
        adjustedTexture.SetPixels(oldTexture.GetPixels());
        Color[] pixels = adjustedTexture.GetPixels();

        // adjust the color of each pixel
        for (int i = 0; i < pixels.Length; i++)
        {
            Color oldColor = pixels[i];
            pixels[i] = new Color(oldColor.r * redValue, oldColor.g * greenValue, oldColor.b * blueValue, oldColor.a);
        }

        adjustedTexture.SetPixels(pixels);
        adjustedTexture.Apply();

        // set adjusted texture as object's main texture
        objectMaterial.SetTexture("_MainTex", adjustedTexture);
    }

    private static void TextureColorAdjuster()
    {
        Initialize();
    }

    private static void ModifyPhotoshopTexture()
    {
        // set the object's color to green (0,1,0) and adjust the texture's color accordingly
        SetObjectColor(Color.green);
        AdjustTextureColor();
    }
    //public ParticleSystem mySubEmitter;

    //void Start()
    //{
    //    ParticleSystem ps = GetComponent<ParticleSystem>();
    //    var sub = ps.subEmitters;
    //    sub.enabled = true;
    //    sub.AddSubEmitter(mySubEmitter, ParticleSystemSubEmitterType.Death, ParticleSystemSubEmitterProperties.InheritNothing);
    //}
    private void RemoveInactiveRenderers()
    {
        UnityEngine.Object[] selectedPrefabs = Selection.objects;

        foreach (UnityEngine.Object prefab in selectedPrefabs)
        {
            string prefabPath = AssetDatabase.GetAssetPath(prefab);
            GameObject prefabObj = PrefabUtility.LoadPrefabContents(prefabPath);
            if (prefabObj != null)
            {
                ParticleSystem[] particleSystems = prefabObj.GetComponentsInChildren<ParticleSystem>(true);

                for(int i = 0;i < particleSystems.Length;i++)
                {
                    var sub = particleSystems[i].subEmitters;
                    ParticleSystemRenderer particleRenderer = particleSystems[i].GetComponent<ParticleSystemRenderer>();
                    UiParticles.UiParticles sparticleSystemsNode = prefabObj.GetComponent<UiParticles.UiParticles>();
                  
                        if (sparticleSystemsNode == null && particleRenderer != null)
                        {
                            if (particleRenderer.enabled == false)
                            {
                                particleRenderer.material = null;
                                // 先移除材质球
                                if (particleRenderer.sharedMaterial != null && particleRenderer.sharedMaterial.name.Contains("Default-ParticleSystem"))
                                {
                                    DestroyImmediate(particleRenderer.material);
                                    Debug.LogError("111111111111");
                                }
                                //else
                                //{
                                //    DestroyImmediate(particleSystems[i], true);
                                //}
                                // 从Prefab中移除ParticleSystem对象
                                if (sub.enabled == false)
                                {
                                    DestroyImmediate(particleSystems[i], true);
                                }

                            }
                        }
                    
                    //else
                    //{
                    //    Debug.LogError ("有UiParticles组建，不移除粒子发射器");
                    //}
                }

                PrefabUtility.SaveAsPrefabAsset(prefabObj, prefabPath);
                PrefabUtility.UnloadPrefabContents(prefabObj);
            }
        }
    }
    
    //获取shader中所有的宏
    public static bool GetShaderKeywords(Shader target, out string[] global, out string[] local)
    {
        try
        {
            MethodInfo globalKeywords = typeof(ShaderUtil).GetMethod("GetShaderGlobalKeywords", BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic);
            global = (string[])globalKeywords.Invoke(null, new object[] { target });
            MethodInfo localKeywords = typeof(ShaderUtil).GetMethod("GetShaderLocalKeywords", BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic);
            local = (string[])localKeywords.Invoke(null, new object[] { target });
            return true;
        }
        catch
        {
            global = local = null;
            return false;
        }
    }
    private static void ClearMaterial()
    {
        UnityEngine.Object[] objs = Selection.GetFiltered(typeof(Material), SelectionMode.DeepAssets);
        for (int i = 0; i < objs.Length; i++)
        {
            EditorUtility.DisplayProgressBar("清理中...", objs[i].name, i / objs.Length);
            Material mat = objs[i] as Material;
            if (GetShaderKeywords(mat.shader, out var global, out var local))
            {
                HashSet<string> keywords = new HashSet<string>();
                foreach (var g in global)
                {
                    keywords.Add(g);
                }
                foreach (var l in local)
                {
                    keywords.Add(l);
                }
                //重置keywords
                List<string> resetKeywords = new List<string>(mat.shaderKeywords);
                foreach (var item in mat.shaderKeywords)
                {
                    if (!keywords.Contains(item))
                        resetKeywords.Remove(item);
                }
                mat.shaderKeywords = resetKeywords.ToArray();
            }
            HashSet<string> property = new HashSet<string>();
            int count = mat.shader.GetPropertyCount();
            for (int ii = 0; ii < count; ii++)
            {
                property.Add(mat.shader.GetPropertyName(ii));
            }
            //MaterialTools.ClearMaterialUnusedProperty(mat);
            SerializedObject o = new SerializedObject(mat);
            SerializedProperty disabledShaderPasses = o.FindProperty("disabledShaderPasses");
            SerializedProperty SavedProperties = o.FindProperty("m_SavedProperties");
            SerializedProperty TexEnvs = SavedProperties.FindPropertyRelative("m_TexEnvs");
            SerializedProperty Floats = SavedProperties.FindPropertyRelative("m_Floats");
            SerializedProperty Colors = SavedProperties.FindPropertyRelative("m_Colors");
            
            //对比属性删除残留的属性
            for (int ii = disabledShaderPasses.arraySize - 1; ii >= 0; ii--)
            {
                if (!property.Contains(disabledShaderPasses.GetArrayElementAtIndex(ii).displayName))
                {
                    disabledShaderPasses.DeleteArrayElementAtIndex(ii);
                }
            }
            for (int ii = TexEnvs.arraySize - 1; ii >= 0; ii--)
            {
                if (!property.Contains(TexEnvs.GetArrayElementAtIndex(ii).displayName))
                {
                    TexEnvs.DeleteArrayElementAtIndex(ii);
                }
            }
            for (int ii = Floats.arraySize - 1; ii >= 0; ii--)
            {
                if (!property.Contains(Floats.GetArrayElementAtIndex(ii).displayName))
                {
                    Floats.DeleteArrayElementAtIndex(ii);
                }
            }
            for (int ii = Colors.arraySize - 1; ii >= 0; ii--)
            {
                if (!property.Contains(Colors.GetArrayElementAtIndex(ii).displayName))
                {
                    Colors.DeleteArrayElementAtIndex(ii);
                }
            }
            o.ApplyModifiedProperties();
            EditorUtility.SetDirty(mat);
        }
        AssetDatabase.SaveAssets();
        EditorUtility.ClearProgressBar();
    }



}




